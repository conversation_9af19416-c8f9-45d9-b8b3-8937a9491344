<template>
  <div class="flex items-center">
    <section class="mr-[6px]">
      <el-input v-model="searchCondition.mediaName" placeholder="按照媒体帐号名称搜索" :prefix-icon="Search" style="width: 240px"
        clearable @change="$emit('search')" />
    </section>
    <section class="mr-[6px]">
      <el-select v-model="searchCondition.platformArr" placeholder="请选择所属平台" clearable filterable multiple
        collapse-tags collapse-tags-tooltip style="width: 240px" @change="$emit('search')">
        <template #prefix>所属平台</template>
        <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.label" />
      </el-select>
    </section>
    <section class="mr-[6px]">
      <el-select v-model="searchCondition.mediaTypeArr" placeholder="请选择媒体类型" clearable filterable multiple collapse-tags
        collapse-tags-tooltip style="width: 240px" @change="$emit('search')">
        <template #prefix>媒体类型</template>
        <el-option v-for="item in rankTypeList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </section>
  </div>
</template>

<script setup>
import { Search } from "@element-plus/icons-vue";

defineProps({
  // 搜索条件
  searchCondition: {
    type: Object,
    required: true,
  },
  rankTypeList: {
    type: Array,
    required: true,
  },
  platformList: {
    type: Array,
    required: true,
  },
});

defineEmits(["search"]);
</script>

<style lang="scss" scoped></style>
