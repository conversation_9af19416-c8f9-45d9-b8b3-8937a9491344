<template>
  <el-dialog :title="dialogTitle" v-model="open" width="600px" :close-on-click-modal="false"
    :close-on-press-escape="false" :show-close="true">
    <el-form :model="virForm" ref="virRef" :rules="virRules" label-width="100px">
      <el-form-item label="媒体号名称" prop="mediaName">
        <el-input v-model="virForm.mediaName" placeholder="请输入媒体号名称" maxlength="20" show-word-limit />
        <!-- <EditPlatformType v-model="virForm.type" :placeholder="'请选择所属平台'" class="w-full" /> -->
      </el-form-item>
      <el-form-item label="所属平台" prop="platform">
        <el-select v-model="virForm.platform" placeholder="请选择所属平台" size="large">
          <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="mediaType">
        <el-select v-model="virForm.mediaType" placeholder="请选择媒体类型" size="large">
          <el-option v-for="item in rankTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dialogCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits,defineProps } from "vue";
// import EditPlatformType from "@/views/publicOpinionManage/components/EditPlatformType.vue";
const emit = defineEmits(["submitForm"]);
const props = defineProps({
  platformList: {
    type: Array,
    required: true,
  },
  rankTypeList: {
    type: Array,
    required: true,
  },
});
const { proxy } = getCurrentInstance();

const data = reactive({
  virForm: {
    status: "0", // 新建时默认启用
  },
  open: false,
  virRules: {
    mediaName: [{ required: true, message: "媒体号名称不可为空", trigger: "submit" }],
    platform: [{ required: true, message: "所属平台不可为空", trigger: "submit" }],
  },
});
const { virForm, open, virRules } = toRefs(data);

const dialogType = ref("add");

const dialogTitle = computed(() => {
  return dialogType.value === "add" ? "新增媒体号" : "编辑媒体号";
});

function openDlg(type, obj) {
  dialogType.value = type;
  virForm.value = {
    ...obj,
  };
  open.value = true;
}

function submitForm() {
  proxy.$refs["virRef"].validate((valid) => {
    if (valid) {
      // console.log("virForm.value", virForm.value);
      emit("submitForm", virForm.value,dialogType.value);
      dialogCancel();
    }
  });
}

function dialogCancel() {
  open.value = false;
  virForm.value = {};
}

defineExpose({
  openDlg,
});
</script>

<style scoped>
:deep(.el-form-item.is-required > .el-form-item__label::after) {
  content: "" !important;
}

/* 
:deep(.el-input__wrapper) {
  background-color: #ffffff !important;
  height: 2.67rem;
  border-radius: 0.5rem;
  border: 0.08rem solid #cfd2d6;
  box-shadow: none;
}

:deep(.el-select__wrapper) {
  border-radius: 0.5rem;
  border: 0.08rem solid #cfd2d6;
  box-shadow: none;
  height: 2.67rem;
} */

:deep(.el-form-item--default) {
  margin-bottom: 18px !important;
}
</style>
